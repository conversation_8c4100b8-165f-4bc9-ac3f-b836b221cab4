import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Dimensions,
  Image,
  Alert,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {
  Text,
  Button,
  Card,
  Chip,
  Surface,
  IconButton,
} from 'react-native-paper';
import { useRoute, useNavigation } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import PagerView from 'react-native-pager-view';

import { useRoomStore } from '../../store/roomStore';
import { useAuthStore } from '../../store/authStore';
import { colors, spacing, typography } from '../../constants';
import { formatPrice } from '../../utils/currency';
import type { GuestNavigationProp } from '../../navigation/GuestNavigator';

const { width } = Dimensions.get('window');

interface RouteParams {
  roomId: string;
}

export const RoomDetailsScreen = () => {
  const route = useRoute();
  const navigation = useNavigation() as GuestNavigationProp;
  const { roomId } = route.params as RouteParams;
  
  const { user } = useAuthStore();
  const { 
    selectedRoom, 
    loading, 
    error, 
    fetchRoomById, 
    checkAvailability,
    setSelectedRoom 
  } = useRoomStore();
  
  const [imageIndex, setImageIndex] = useState(0);
  const [checkingAvailability, setCheckingAvailability] = useState(false);
  const pagerRef = useRef<PagerView>(null);

  useEffect(() => {
    loadRoomDetails();
  }, [roomId]);

  useEffect(() => {
    return () => {
      setSelectedRoom(null);
    };
  }, []);

  const loadRoomDetails = async () => {
    await fetchRoomById(roomId);
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading room details...</Text>
      </View>
    );
  }

  if (error || !selectedRoom) {
    return (
      <View style={styles.errorContainer}>
        <MaterialIcons name="error-outline" size={64} color={colors.error} />
        <Text style={styles.errorTitle}>Room Not Found</Text>
        <Text style={styles.errorMessage}>
          {error || 'Unable to load room details'}
        </Text>
        <Button
          mode="outlined"
          onPress={loadRoomDetails}
          style={styles.retryButton}
        >
          Retry
        </Button>
      </View>
    );
  }

  const room = selectedRoom;

  const handleBookNowPress = async () => {
    if (!user) {
      Alert.alert(
        'Sign In Required',
        'Please sign in to make a reservation.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Sign In', onPress: () => navigation.navigate('Auth') }
        ]
      );
      return;
    }

    // Navigate to booking screen with proper parameters
    // Using default dates for now - in a real app, you'd get these from a date picker
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const checkIn = today.toISOString().split('T')[0];
    const checkOut = tomorrow.toISOString().split('T')[0];

    navigation.navigate('Booking', {
      room: room,
      checkIn,
      checkOut,
      guests: 1
    });
  };

  const getRoomTypeIcon = (type: string) => {
    switch (type) {
      case 'standard':
        return 'bed';
      case 'deluxe':
        return 'bed-queen';
      case 'suite':
        return 'domain';
      case 'presidential':
        return 'crown';
      default:
        return 'bed';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return colors.success;
      case 'booked':
        return colors.error;
      case 'maintenance':
        return colors.warning;
      case 'cleaning':
        return colors.info;
      default:
        return colors.textSecondary;
    }
  };



  const renderImagePager = () => {
    if (!room.images || room.images.length === 0) {
      return (
        <View style={styles.noImageContainer}>
          <MaterialIcons name="image-not-supported" size={64} color={colors.textSecondary} />
          <Text style={styles.noImageText}>No images available</Text>
        </View>
      );
    }

    // Handle both legacy string arrays and new RoomImage objects
    const imageItems = room.images.map((image, index) => {
      if (typeof image === 'string') {
        // Legacy format - convert to RoomImage-like object
        return {
          id: `legacy_${index}`,
          url: image,
          alt_text: `Room ${room.room_number} image ${index + 1}`,
        };
      }
      // New format - already a RoomImage object
      return image;
    });

    return (
      <View style={styles.imageContainer}>
        <PagerView
          ref={pagerRef}
          style={styles.pager}
          initialPage={0}
          onPageSelected={(e) => setImageIndex(e.nativeEvent.position)}
        >
          {imageItems.map((image, index) => (
            <View key={image.id} style={styles.imagePage}>
              <Image
                source={{ uri: image.url }}
                style={styles.roomImage}
                resizeMode="cover"
                accessibilityLabel={image.alt_text || `Room image ${index + 1}`}
              />
            </View>
          ))}
        </PagerView>

        {imageItems.length > 1 && (
          <>
            <View style={styles.imageIndicators}>
              {imageItems.map((image, index) => (
                <View
                  key={`indicator_${image.id}`}
                  style={[
                    styles.indicator,
                    index === imageIndex && styles.activeIndicator
                  ]}
                />
              ))}
            </View>

            <IconButton
              icon="chevron-left"
              size={24}
              iconColor={colors.white}
              containerColor="rgba(0,0,0,0.5)"
              style={[styles.imageNavButton, styles.prevButton]}
              onPress={() => {
                const prevIndex = imageIndex > 0 ? imageIndex - 1 : imageItems.length - 1;
                pagerRef.current?.setPage(prevIndex);
              }}
            />

            <IconButton
              icon="chevron-right"
              size={24}
              iconColor={colors.white}
              containerColor="rgba(0,0,0,0.5)"
              style={[styles.imageNavButton, styles.nextButton]}
              onPress={() => {
                const nextIndex = imageIndex < imageItems.length - 1 ? imageIndex + 1 : 0;
                pagerRef.current?.setPage(nextIndex);
              }}
            />
          </>
        )}
      </View>
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Room Images */}
      {renderImagePager()}

      <View style={styles.content}>
        {/* Room Header */}
        <View style={styles.roomHeader}>
          <View style={styles.roomTitleContainer}>
            <Text style={styles.roomNumber}>Room {room.room_number}</Text>
            <View style={styles.roomTypeContainer}>
              <MaterialIcons
                name={getRoomTypeIcon(room.room_type)}
                size={20}
                color={colors.primary}
              />
              <Text style={styles.roomType}>{room.room_type}</Text>
            </View>
          </View>
          
          <Chip
            mode="outlined"
            textStyle={[styles.statusText, { color: getStatusColor(room.status) }]}
            style={[styles.statusChip, { borderColor: getStatusColor(room.status) }]}
            compact={false}
          >
            {room.status}
          </Chip>
        </View>

        {/* Price */}
        <Surface style={styles.priceCard}>
          <Text style={styles.priceLabel}>Price per night</Text>
          <Text style={styles.price}>{formatPrice(room.price_per_night)}</Text>
        </Surface>

        {/* Room Info */}
        <Card style={styles.infoCard}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Room Information</Text>
            <View style={styles.infoGrid}>
              <View style={styles.infoItem}>
                <MaterialIcons name="people" size={24} color={colors.primary} />
                <View>
                  <Text style={styles.infoLabel}>Max Occupancy</Text>
                  <Text style={styles.infoValue}>{room.max_occupancy} guests</Text>
                </View>
              </View>

              <View style={styles.infoItem}>
                <MaterialIcons name="square-foot" size={24} color={colors.primary} />
                <View>
                  <Text style={styles.infoLabel}>Room Type</Text>
                  <Text style={styles.infoValue}>{room.room_type}</Text>
                </View>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Description */}
        <Card style={styles.descriptionCard}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>{room.description}</Text>
          </Card.Content>
        </Card>

        {/* Amenities */}
        {room.amenities && room.amenities.length > 0 && (
          <Card style={styles.amenitiesCard}>
            <Card.Content>
              <Text style={styles.sectionTitle}>Amenities</Text>
              <View style={styles.amenitiesGrid}>
                {room.amenities.map((amenity, index) => (
                  <View key={index} style={styles.amenityItem}>
                    <MaterialIcons name="check" size={16} color={colors.success} />
                    <Text style={styles.amenityText}>{amenity}</Text>
                  </View>
                ))}
              </View>
            </Card.Content>
          </Card>
        )}

        {/* Booking Button */}
        <View style={styles.bookingSection}>
          <TouchableOpacity
            onPress={handleBookNowPress}
            disabled={room.status !== 'available' || checkingAvailability}
            style={[
              styles.bookButton,
              {
                backgroundColor: room.status !== 'available' ? colors.disabled : colors.primary,
                opacity: (room.status !== 'available' || checkingAvailability) ? 0.6 : 1,
              }
            ]}
            activeOpacity={0.8}
          >
            {checkingAvailability ? (
              <ActivityIndicator size="small" color="#FFFFFF" style={{ marginRight: 8 }} />
            ) : null}
            <Text style={styles.bookButtonText}>
              {checkingAvailability
                ? 'Checking...'
                : room.status !== 'available'
                ? `Room ${room.status.charAt(0).toUpperCase() + room.status.slice(1)}`
                : 'Book Now'
              }
            </Text>
          </TouchableOpacity>

          <Text style={styles.bookingNote}>
            {room.status === 'available'
              ? 'Free cancellation within 24 hours'
              : 'This room is currently not available for booking'
            }
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  loadingText: {
    ...typography.body,
    marginTop: spacing.md,
    color: colors.textSecondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  errorTitle: {
    ...typography.h3,
    fontWeight: 'bold',
    marginTop: spacing.md,
    marginBottom: spacing.sm,
    color: colors.textPrimary,
  },
  errorMessage: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  retryButton: {
    marginTop: spacing.md,
  },
  imageContainer: {
    height: 300,
    position: 'relative',
  },
  pager: {
    flex: 1,
  },
  imagePage: {
    flex: 1,
  },
  roomImage: {
    width: '100%',
    height: '100%',
  },
  noImageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.surface,
  },
  noImageText: {
    ...typography.body,
    color: colors.textSecondary,
    marginTop: spacing.sm,
  },
  imageIndicators: {
    position: 'absolute',
    bottom: spacing.md,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: spacing.sm,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255,255,255,0.5)',
  },
  activeIndicator: {
    backgroundColor: colors.white,
  },
  imageNavButton: {
    position: 'absolute',
    top: '50%',
    marginTop: -20,
  },
  prevButton: {
    left: spacing.md,
  },
  nextButton: {
    right: spacing.md,
  },
  content: {
    padding: spacing.md,
  },
  roomHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
    minHeight: 80,
  },
  roomTitleContainer: {
    flex: 1,
    paddingRight: spacing.md,
  },
  roomNumber: {
    ...typography.h2,
    fontWeight: 'bold',
    color: colors.textPrimary,
    marginBottom: spacing.sm,
    fontSize: 28,
  },
  roomTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    marginTop: 4,
  },
  roomType: {
    ...typography.subtitle,
    color: colors.textPrimary,
    textTransform: 'capitalize',
    fontWeight: '600',
  },
  statusChip: {
    marginTop: spacing.sm,
    minWidth: 80,
    height: 32,
    paddingHorizontal: 8,
  },
  statusText: {
    fontSize: 12,
    textTransform: 'capitalize',
    fontWeight: '500',
  },
  priceCard: {
    padding: spacing.md,
    marginBottom: spacing.md,
    borderRadius: 8,
    elevation: 2,
  },
  priceLabel: {
    ...typography.body,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
    fontSize: 14,
    fontWeight: '500',
  },
  price: {
    ...typography.h2,
    fontWeight: 'bold',
    color: colors.primary,
    fontSize: 28,
  },
  sectionTitle: {
    ...typography.h4,
    fontWeight: 'bold',
    color: colors.textPrimary,
    marginBottom: spacing.md,
    fontSize: 20,
  },
  infoCard: {
    marginBottom: spacing.md,
  },
  infoGrid: {
    gap: spacing.md,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  infoLabel: {
    ...typography.caption,
    color: colors.textSecondary,
    fontSize: 12,
    marginBottom: 2,
  },
  infoValue: {
    ...typography.body,
    fontWeight: '600',
    color: colors.textPrimary,
    fontSize: 16,
  },
  descriptionCard: {
    marginBottom: spacing.md,
  },
  description: {
    ...typography.body,
    color: colors.textPrimary,
    lineHeight: 24,
    fontSize: 16,
    fontWeight: '400',
  },
  amenitiesCard: {
    marginBottom: spacing.md,
  },
  amenitiesGrid: {
    gap: spacing.sm,
  },
  amenityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  amenityText: {
    ...typography.body,
    color: colors.textPrimary,
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
  bookingSection: {
    marginTop: spacing.lg,
    marginBottom: spacing.xl,
  },
  bookButton: {
    marginBottom: spacing.md,
    borderRadius: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    height: 56,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  bookButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  bookingNote: {
    ...typography.caption,
    color: colors.textSecondary,
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '400',
    marginTop: 8,
  },
});
